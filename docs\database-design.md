# 数据库设计方案

基于 Cloudflare D1 + KV 的混合存储架构，为软件许可证验证服务系统提供高性能、低延迟的数据存储解决方案。

## 1. 架构概述

### 存储分层策略

- **Cloudflare D1**：主数据库，存储核心业务数据
- **Cloudflare KV**：缓存层，存储高频访问的验证数据
- **数据同步**：D1 → KV 的单向同步机制

### 性能优化原则

- **读写分离**：验证读取走 KV，管理操作走 D1
- **缓存优先**：热数据优先从 KV 读取
- **异步同步**：D1 数据变更后异步更新 KV

## 2. Cloudflare D1 数据库设计

### 2.1 管理员表 (admins)

```sql
CREATE TABLE admins (
  id TEXT PRIMARY KEY,                    -- UUID格式的管理员ID
  username TEXT UNIQUE NOT NULL,          -- 用户名
  password_hash TEXT NOT NULL,            -- 密码哈希值
  role TEXT NOT NULL CHECK (role IN ('super', 'normal')), -- 角色：超级管理员/普通管理员
  created_by TEXT,                        -- 创建者ID，超级管理员为NULL
  authorized_products TEXT,               -- JSON数组，普通管理员可操作的产品ID列表
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'disabled')), -- 账号状态
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login DATETIME,                    -- 最后登录时间
  FOREIGN KEY (created_by) REFERENCES admins(id)
);

-- 索引
CREATE INDEX idx_admins_username ON admins(username);
CREATE INDEX idx_admins_role ON admins(role);
CREATE INDEX idx_admins_created_by ON admins(created_by);
```

### 2.2 软件产品表 (products)

```sql
CREATE TABLE products (
  id TEXT PRIMARY KEY,                    -- UUID格式的产品ID
  name TEXT NOT NULL,                     -- 产品名称
  description TEXT,                       -- 产品描述
  price DECIMAL(10,2) NOT NULL,           -- 产品价格
  verification_strategies TEXT NOT NULL,  -- JSON格式的验证策略配置
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'disabled')), -- 产品状态
  created_by TEXT NOT NULL,               -- 创建者ID（超级管理员）
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES admins(id)
);

-- 索引
CREATE INDEX idx_products_created_by ON products(created_by);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_name ON products(name);
```

### 2.3 许可证表 (licenses)

```sql
CREATE TABLE licenses (
  id TEXT PRIMARY KEY,                    -- UUID格式的许可证ID
  license_key TEXT UNIQUE NOT NULL,       -- 许可证密钥
  product_id TEXT NOT NULL,               -- 关联产品ID
  admin_id TEXT NOT NULL,                 -- 生成者ID
  price DECIMAL(10,2) NOT NULL,           -- 销售价格
  expiry_date DATETIME,                   -- 过期时间，NULL表示永久许可证
  device_limit INTEGER DEFAULT 1,         -- 设备数量限制
  features TEXT,                          -- JSON数组，允许的功能列表
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'revoked', 'expired')), -- 许可证状态
  metadata TEXT,                          -- JSON格式的额外元数据
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  activated_at DATETIME,                  -- 首次激活时间
  last_verified DATETIME,                 -- 最后验证时间
  FOREIGN KEY (product_id) REFERENCES products(id),
  FOREIGN KEY (admin_id) REFERENCES admins(id)
);

-- 索引
CREATE INDEX idx_licenses_license_key ON licenses(license_key);
CREATE INDEX idx_licenses_product_id ON licenses(product_id);
CREATE INDEX idx_licenses_admin_id ON licenses(admin_id);
CREATE INDEX idx_licenses_status ON licenses(status);
CREATE INDEX idx_licenses_expiry_date ON licenses(expiry_date);
CREATE INDEX idx_licenses_created_at ON licenses(created_at);
```

### 2.4 设备绑定表 (devices)

```sql
CREATE TABLE devices (
  id TEXT PRIMARY KEY,                    -- UUID格式的设备ID
  license_id TEXT NOT NULL,               -- 关联许可证ID
  device_fingerprint TEXT NOT NULL,       -- 设备指纹
  device_info TEXT,                       -- JSON格式的设备信息
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'unbound')), -- 绑定状态
  first_seen DATETIME DEFAULT CURRENT_TIMESTAMP, -- 首次绑定时间
  last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 最后活跃时间
  FOREIGN KEY (license_id) REFERENCES licenses(id),
  UNIQUE(license_id, device_fingerprint)
);

-- 索引
CREATE INDEX idx_devices_license_id ON devices(license_id);
CREATE INDEX idx_devices_fingerprint ON devices(device_fingerprint);
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_devices_last_seen ON devices(last_seen);
```

### 2.5 验证日志表 (verification_logs)

```sql
CREATE TABLE verification_logs (
  id TEXT PRIMARY KEY,                    -- UUID格式的日志ID
  license_id TEXT NOT NULL,               -- 关联许可证ID
  device_fingerprint TEXT,                -- 设备指纹
  result TEXT NOT NULL CHECK (result IN ('success', 'expired', 'device_limit', 'feature_denied', 'revoked', 'not_found', 'invalid')), -- 验证结果
  requested_features TEXT,                -- JSON数组，请求的功能列表
  granted_features TEXT,                  -- JSON数组，授权的功能列表
  ip_address TEXT,                        -- 请求IP地址
  user_agent TEXT,                        -- 用户代理
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  response_time INTEGER,                  -- 响应时间（毫秒）
  FOREIGN KEY (license_id) REFERENCES licenses(id)
);

-- 索引
CREATE INDEX idx_verification_logs_license_id ON verification_logs(license_id);
CREATE INDEX idx_verification_logs_timestamp ON verification_logs(timestamp);
CREATE INDEX idx_verification_logs_result ON verification_logs(result);
CREATE INDEX idx_verification_logs_device_fingerprint ON verification_logs(device_fingerprint);
```

### 2.6 订单表 (orders)

```sql
CREATE TABLE orders (
  id TEXT PRIMARY KEY,                    -- UUID格式的订单ID
  order_number TEXT UNIQUE NOT NULL,      -- 订单号（用户友好）
  license_id TEXT UNIQUE NOT NULL,        -- 关联许可证ID（1:1关系）
  admin_id TEXT NOT NULL,                 -- 销售员ID
  channel TEXT,                           -- 销售渠道（便于后续扩展）
  customer_name TEXT,                     -- 客户名称（便于后续扩展）
  remarks TEXT,                           -- 备注信息（便于后续扩展）
  amount DECIMAL(10,2) NOT NULL,          -- 订单金额
  status TEXT DEFAULT 'generated' CHECK (status IN ('generated', 'sold', 'refunded')), -- 销售状态
  refund_reason TEXT,                     -- 退款原因
  sold_at DATETIME,                       -- 确认售出时间
  refunded_at DATETIME,                   -- 退款时间
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (license_id) REFERENCES licenses(id),
  FOREIGN KEY (admin_id) REFERENCES admins(id)
);

-- 索引
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_orders_license_id ON orders(license_id);
CREATE INDEX idx_orders_admin_id ON orders(admin_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_channel ON orders(channel);
CREATE INDEX idx_orders_customer_name ON orders(customer_name);
```

## 3. Cloudflare KV 缓存设计

### 3.1 许可证验证缓存

**Key 格式**: `license:verify:{license_key}`
**Value 结构**:

```json
{
  "license_id": "lic_123",
  "product_id": "prod_456",
  "status": "active",
  "expiry_date": "2024-12-31T23:59:59Z",
  "device_limit": 3,
  "features": ["basic", "advanced"],
  "current_devices": 2,
  "last_updated": "2024-01-15T10:30:00Z"
}
```

**TTL**: 300 秒（5 分钟）

### 3.2 设备绑定缓存

**Key 格式**: `device:binding:{license_id}`
**Value 结构**:

```json
{
  "device_count": 2,
  "device_limit": 3,
  "devices": [
    {
      "fingerprint": "device_001",
      "last_seen": "2024-01-15T10:30:00Z"
    }
  ],
  "last_updated": "2024-01-15T10:30:00Z"
}
```

**TTL**: 3600 秒（1 小时）

### 3.3 产品配置缓存

**Key 格式**: `product:config:{product_id}`
**Value 结构**:

```json
{
  "name": "My Software v1.0",
  "verification_strategies": {
    "expiry": true,
    "device_limit": 3,
    "features": ["basic", "advanced", "premium"]
  },
  "status": "active",
  "last_updated": "2024-01-15T10:30:00Z"
}
```

**TTL**: 3600 秒（1 小时）

### 3.4 管理员权限缓存

**Key 格式**: `admin:auth:{admin_id}`
**Value 结构**:

```json
{
  "username": "admin_user",
  "role": "super",
  "authorized_products": ["prod_123", "prod_456"],
  "status": "active",
  "last_updated": "2024-01-15T10:30:00Z"
}
```

**TTL**: 1800 秒（30 分钟）

### 3.5 订单缓存

**Key 格式**: `order:detail:{license_id}`
**Value 结构**:

```json
{
  "order_id": "ord_123",
  "order_number": "ORD-20240115-001",
  "license_id": "lic_456",
  "admin_id": "admin_123",
  "channel": "官网直销",
  "customer_name": "张三",
  "remarks": "企业客户，需要发票",
  "amount": 99.99,
  "status": "sold",
  "sold_at": "2024-01-15T10:30:00Z",
  "refund_reason": null,
  "last_updated": "2024-01-15T10:30:00Z"
}
```

**TTL**: 1800 秒（30 分钟）

### 3.6 统计数据缓存

**Key 格式**: `stats:{admin_id}:{period}`
**Value 结构**:

```json
{
  "total_revenue": 9999.99,
  "total_orders": 50,
  "sold_orders": 45,
  "refunded_orders": 2,
  "refunded_amount": 199.98,
  "period": "30d",
  "products": [
    {
      "product_id": "prod_123",
      "product_name": "My Software v1.0",
      "revenue": 4999.99,
      "orders_count": 25,
      "sold_count": 23,
      "refunded_count": 2
    }
  ],
  "last_updated": "2024-01-15T10:30:00Z"
}
```

**TTL**: 900 秒（15 分钟）

## 4. 数据同步策略

### 4.1 实时同步场景

- **许可证状态变更**：撤销、过期状态立即同步到 KV
- **设备绑定变更**：新设备绑定立即更新 KV 缓存
- **产品配置变更**：验证策略修改立即同步
- **订单状态变更**：订单支付确认、退款等状态变更立即同步
- **订单创建**：新订单创建后立即缓存订单详情

### 4.2 定时同步场景

- **统计数据更新**：每 15 分钟基于订单数据重新计算统计缓存
- **设备活跃状态**：每小时同步设备最后活跃时间
- **过期许可证清理**：每日清理过期许可证缓存
- **订单数据清理**：定期清理已完成订单的详细缓存

### 4.3 缓存失效策略

- **主动失效**：数据变更时主动删除相关 KV 缓存
- **被动失效**：依赖 TTL 自动过期
- **强制刷新**：管理员可手动刷新缓存

## 5. 性能优化

### 5.1 查询优化

- **复合索引**：为常用查询组合创建复合索引
- **分页查询**：大数据量查询使用 LIMIT 和 OFFSET
- **预聚合**：统计数据预先计算并缓存

### 5.2 写入优化

- **批量操作**：批量插入和更新操作
- **异步写入**：非关键数据异步写入
- **事务控制**：关键操作使用事务保证一致性

### 5.3 存储优化

- **数据压缩**：JSON 数据使用压缩存储
- **冷热分离**：历史数据定期归档
- **索引优化**：定期分析和优化索引使用

## 6. 安全考虑

### 6.1 数据加密

- **敏感字段加密**：密码哈希、设备指纹等敏感数据加密存储
- **传输加密**：所有数据传输使用 HTTPS
- **密钥管理**：加密密钥安全存储和轮换

### 6.2 访问控制

- **连接限制**：限制数据库连接数和访问频率
- **权限隔离**：不同角色使用不同的数据库权限
- **审计日志**：记录所有数据库操作日志

### 6.3 数据备份

- **自动备份**：D1 数据库自动备份
- **跨区域复制**：关键数据跨区域备份
- **恢复测试**：定期测试数据恢复流程

## 7. 数据库初始化

### 7.1 环境配置

```bash
# 创建D1数据库
wrangler d1 create verify-db

# 创建KV命名空间
wrangler kv:namespace create "VERIFY_CACHE"
wrangler kv:namespace create "VERIFY_CACHE" --preview
```

### 7.2 wrangler.toml 配置

```toml
[[d1_databases]]
binding = "DB"
database_name = "verify-db"
database_id = "your-database-id"

[[kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
```

### 7.3 初始化数据

```sql
-- 创建默认超级管理员
INSERT INTO admins (id, username, password_hash, role)
VALUES (
  'admin_001',
  'admin',
  '$2b$10$example_hash_here',
  'super'
);

-- 创建示例产品
INSERT INTO products (id, name, description, price, verification_strategies, created_by)
VALUES (
  'prod_001',
  'Demo Software',
  'Demo software for testing',
  99.99,
  '{"expiry": true, "device_limit": 3, "features": ["basic", "advanced"]}',
  'admin_001'
);

-- 创建示例许可证
INSERT INTO licenses (id, license_key, product_id, admin_id, price, expiry_date, device_limit, features)
VALUES (
  'lic_001',
  'DEMO-XXXX-XXXX-XXXX',
  'prod_001',
  'admin_001',
  99.99,
  '2024-12-31 23:59:59',
  3,
  '["basic", "advanced"]'
);

-- 创建示例订单
INSERT INTO orders (id, order_number, license_id, admin_id, channel, customer_name, remarks, amount, status, sold_at)
VALUES (
  'ord_001',
  'ORD-20240115-001',
  'lic_001',
  'admin_001',
  '官网直销',
  '张三',
  '企业客户，需要发票',
  99.99,
  'sold',
  '2024-01-15 10:30:00'
);
```

## 8. 数据迁移策略

### 8.1 版本控制

- **迁移文件命名**：`YYYY-MM-DD-HH-MM-description.sql`
- **版本记录表**：记录已执行的迁移版本
- **回滚机制**：每个迁移提供回滚脚本

### 8.2 迁移执行

- **增量迁移**：只执行未运行的迁移脚本
- **事务保护**：每个迁移在事务中执行
- **验证机制**：迁移后验证数据完整性

### 8.3 数据一致性

- **外键约束**：确保引用完整性
- **数据校验**：定期校验数据一致性
- **修复机制**：自动修复数据不一致问题

## 9. 监控和维护

### 9.1 性能监控

- **查询性能**：监控慢查询和执行计划
- **缓存命中率**：监控 KV 缓存命中率
- **存储使用**：监控数据库存储使用情况

### 9.2 数据质量

- **数据完整性检查**：定期检查外键约束
- **重复数据检测**：检测和清理重复数据
- **数据一致性验证**：D1 和 KV 数据一致性检查

### 9.3 维护任务

- **定期清理**：清理过期日志和无效数据
- **索引维护**：定期重建和优化索引
- **统计更新**：更新表统计信息

## 10. 扩展性考虑

### 10.1 水平扩展

- **分片策略**：按产品或管理员分片
- **读写分离**：读操作走 KV，写操作走 D1
- **负载均衡**：多个 Worker 实例负载均衡

### 10.2 垂直扩展

- **存储优化**：压缩和归档历史数据
- **查询优化**：优化复杂查询和索引
- **缓存扩展**：增加缓存层级和策略

### 10.3 容量规划

- **数据增长预测**：基于业务增长预测数据量
- **性能基准**：建立性能基准和监控指标
- **扩容策略**：制定自动和手动扩容策略
