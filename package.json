{"name": "cloudflare-workers-openapi", "version": "0.0.1", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "cf-typegen": "wrangler types"}, "dependencies": {"chanfana": "^2.6.3", "hono": "^4.6.20", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "22.13.0", "@types/service-worker-mock": "^2.0.4", "wrangler": "^4.26.1"}}